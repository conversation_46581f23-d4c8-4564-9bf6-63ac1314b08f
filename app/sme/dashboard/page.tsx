"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardCard, QuickAction } from "@/components/ui/dashboard-card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Check,
    Clock,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - in real app, this would come from API
  const financialScore = 78;
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const investorInterest = 15;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "investor", message: "3 new investors viewed your profile", time: "5 hours ago", status: "info" },
    { id: 3, type: "score", message: "Financial health score improved by 5 points", time: "1 day ago", status: "success" },
    { id: 4, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "pending" }
  ];

  const actionItems = [
    { id: 1, task: "Upload bank statements (last 12 months)", priority: "high", completed: false },
    { id: 2, task: "Complete business profile information", priority: "medium", completed: false },
    { id: 3, task: "Schedule advisor consultation call", priority: "high", completed: true },
    { id: 4, task: "Review investor interest notifications", priority: "medium", completed: false }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-300 bg-red-50';
      case 'medium': return 'border-amber-300 bg-amber-50';
      case 'low': return 'border-gray-300 bg-gray-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-black mb-2">
                Welcome back, {user?.companyName || user?.name || 'User'}
              </h1>
              <p className="text-gray-600">Here's your business performance overview</p>
            </div>
            <Badge variant="default" className="bg-black text-white">
              Profile {profileCompletion}% Complete
            </Badge>
          </div>
        </motion.div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Financial Health Score */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <DashboardCard
              title="Financial Health Score"
              value={`${financialScore}/100`}
              icon={<TrendingUp className="w-6 h-6" />}
              badge="+5 this month"
              progress={financialScore}
              description="Your overall financial wellness rating"
            />
          </motion.div>

          {/* Documents Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <DashboardCard
              title="Documents Uploaded"
              value={`${documentsUploaded}/${totalDocuments}`}
              icon={<FileText className="w-6 h-6" />}
              progress={(documentsUploaded / totalDocuments) * 100}
              action={{
                label: "Upload",
                onClick: () => router.push('/sme/dashboard/upload'),
                variant: 'ghost'
              }}
            />
          </motion.div>

          {/* Investor Interest */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <DashboardCard
              title="Investor Views"
              value={investorInterest}
              icon={<Users className="w-6 h-6" />}
              badge="+3 today"
              description="High interest level"
            />
          </motion.div>

          {/* 10X Growth Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <DashboardCard
              title="10X Growth Program"
              value="45% Complete"
              icon={<Target className="w-6 h-6" />}
              badge="Enrolled"
              action={{
                label: "Enter Program",
                onClick: () => router.push('/sme/dashboard/10x-growth'),
                variant: 'default'
              }}
            />
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Action Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Check className="w-5 h-5 mr-2" />
                  Action Items
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Complete these tasks to improve your financial health score
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {actionItems.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center justify-between p-4 rounded-lg border ${
                        item.completed ? "bg-green-50 border-green-200" : getPriorityColor(item.priority)
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {item.completed ? (
                          <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                            <Check className="w-3 h-3 text-white" />
                          </div>
                        ) : (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            item.priority === "high" ? "border-red-500" :
                            item.priority === "medium" ? "border-amber-500" : "border-gray-400"
                          }`} />
                        )}
                        <div>
                          <p className={`text-sm font-medium ${
                            item.completed ? "text-green-800" : "text-gray-900"
                          }`}>
                            {item.task}
                          </p>
                          <p className="text-xs text-gray-600 capitalize">
                            {item.priority} priority
                          </p>
                        </div>
                      </div>
                      {!item.completed && (
                        <Button 
                          variant="outline-gray" 
                          size="sm"
                          className="shrink-0"
                        >
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === "success" ? "bg-green-500" :
                        activity.status === "info" ? "bg-blue-500" : "bg-amber-500"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-sm font-medium">{activity.message}</p>
                        <p className="text-gray-500 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-8"
        >
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-black">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <QuickAction
                  label="Upload Documents"
                  icon={<Upload className="w-4 h-4" />}
                  href="/sme/dashboard/upload"
                  variant="primary"
                />
                <QuickAction
                  label="View Reports"
                  icon={<BarChart3 className="w-4 h-4" />}
                  href="/sme/dashboard/reports"
                  variant="secondary"
                />
                <QuickAction
                  label="Schedule Call"
                  icon={<Users className="w-4 h-4" />}
                  href="/sme/dashboard/advisor-call"
                  variant="secondary"
                />
                <QuickAction
                  label="10X Growth Program"
                  icon={<Target className="w-4 h-4" />}
                  href="/sme/dashboard/10x-growth"
                  variant="secondary"
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
