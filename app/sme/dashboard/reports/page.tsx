"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    BarChart3,
    Calendar,
    Download,
    Eye,
    FileText,
    Target,
    TrendingDown,
    TrendingUp,
    Users
} from "lucide-react";

export default function ReportsPage() {
  const { user } = useAuthStore();

  const reports = [
    {
      id: 1,
      title: "Financial Health Score Report",
      description: "Comprehensive analysis of your business financial health",
      score: 78,
      trend: "up",
      lastUpdated: "2 days ago",
      type: "financial",
      icon: BarChart3
    },
    {
      id: 2,
      title: "Investor Readiness Report",
      description: "Assessment of your readiness to attract investors",
      score: 85,
      trend: "up",
      lastUpdated: "1 week ago",
      type: "investor",
      icon: Users
    },
    {
      id: 3,
      title: "Growth Potential Analysis",
      description: "Market opportunities and growth projections",
      score: 72,
      trend: "down",
      lastUpdated: "3 days ago",
      type: "growth",
      icon: Target
    },
    {
      id: 4,
      title: "Monthly Performance Report",
      description: "Key metrics and performance indicators for this month",
      score: 91,
      trend: "up",
      lastUpdated: "1 day ago",
      type: "performance",
      icon: TrendingUp
    }
  ];

  const keyMetrics = [
    { label: "Revenue Growth", value: "+23%", trend: "up", color: "emerald" },
    { label: "Profit Margin", value: "18.5%", trend: "up", color: "blue" },
    { label: "Cash Flow", value: "$45K", trend: "down", color: "amber" },
    { label: "Investor Interest", value: "15 views", trend: "up", color: "purple" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />

      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-black mb-2">Business Reports</h1>
              <p className="text-gray-600">Comprehensive insights and analytics for your business</p>
            </div>
            <Button className="bg-black hover:bg-gray-800 text-white">
              <Download className="w-4 h-4 mr-2" />
              Export All Reports
            </Button>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          {keyMetrics.map((metric, index) => (
            <Card key={index} className="bg-white border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">{metric.label}</p>
                    <p className="text-2xl font-bold text-black mt-1">{metric.value}</p>
                  </div>
                  <div className="p-2 rounded-lg bg-gray-100">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="w-5 h-5 text-green-600" />
                    ) : (
                      <TrendingDown className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Reports Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {reports.map((report, index) => (
            <motion.div
              key={report.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
            >
              <Card className="bg-white border-gray-200 shadow-lg hover:border-gray-400 transition-all duration-300 h-full">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                        <report.icon className="w-6 h-6 text-black" />
                      </div>
                      <div>
                        <CardTitle className="text-black text-lg">{report.title}</CardTitle>
                        <div className="flex items-center mt-2">
                          <Badge className={`mr-2 ${
                            report.score >= 80 ? 'bg-green-100 text-green-700 border-green-200' :
                            report.score >= 60 ? 'bg-amber-100 text-amber-700 border-amber-200' :
                            'bg-red-100 text-red-700 border-red-200'
                          }`}>
                            Score: {report.score}
                          </Badge>
                          {report.trend === 'up' ? (
                            <TrendingUp className="w-4 h-4 text-green-600" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-600" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 mb-4">
                    {report.description}
                  </CardDescription>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="w-4 h-4 mr-1" />
                      Updated {report.lastUpdated}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                        <Download className="w-4 h-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Report Generation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-8"
        >
          <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
            <CardContent className="p-8">
              <div className="text-center">
                <FileText className="w-12 h-12 text-black mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-black mb-2">Generate Custom Report</h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Need a specific analysis? Generate custom reports tailored to your business needs and investor requirements.
                </p>
                <div className="flex justify-center">
                  <Button className="bg-black hover:bg-gray-800 text-white">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Generate Financial Report
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
