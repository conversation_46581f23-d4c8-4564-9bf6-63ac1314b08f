"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import {
    BarChart3,
    Calendar,
    CheckCircle,
    Star,
    Target,
    TrendingUp,
    Video
} from "lucide-react";
import { useState } from "react";



interface Milestone {
  id: number;
  title: string;
  description: string;
  target: string;
  current: string;
  progress: number;
  status: "completed" | "in-progress" | "pending";
  reward: string;
}

export default function SME10xGrowthProgram() {
  const [activeTab, setActiveTab] = useState("dashboard");

  // Mock user progress data
  const userProgress = {
    overallProgress: 45,
    completedModules: 3,
    totalModules: 8,
    currentStreak: 7,
    totalPoints: 1850,
    level: "Growth Accelerator",
    nextMilestone: "Revenue Optimization",
    programStartDate: "2024-01-15",
    estimatedCompletion: "2024-04-15"
  };



  const milestones: Milestone[] = [
    {
      id: 1,
      title: "Revenue Growth",
      description: "Achieve 30% revenue increase from baseline",
      target: "$500K",
      current: "$385K",
      progress: 77,
      status: "in-progress",
      reward: "Growth Champion Badge + $500 bonus"
    },
    {
      id: 2,
      title: "Customer Acquisition",
      description: "Reach 1000 active customers",
      target: "1000",
      current: "650",
      progress: 65,
      status: "in-progress",
      reward: "Customer Master Certificate"
    },
    {
      id: 3,
      title: "Operational Efficiency",
      description: "Reduce operational costs by 20%",
      target: "20%",
      current: "12%",
      progress: 60,
      status: "in-progress",
      reward: "Efficiency Expert Badge"
    }
  ];

  const upcomingSessions = [
    {
      id: 1,
      title: "1-on-1 Growth Coaching",
      date: "Tomorrow",
      time: "2:00 PM",
      type: "coaching",
      mentor: "Assigned Advisor"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-700 border-green-200";
      case "current": case "in-progress": return "bg-blue-100 text-blue-700 border-blue-200";
      case "locked": case "pending": return "bg-gray-100 text-gray-600 border-gray-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />
      
      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Program Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-3xl font-bold text-black">10X Growth Program</h1>
              <Badge className="bg-gradient-to-r from-yellow-100 to-orange-100 text-amber-700 border-amber-200">
                <Star className="w-4 h-4 mr-1" />
                {userProgress.level}
              </Badge>
            </div>
            <p className="text-gray-600">Transform your business with our comprehensive growth acceleration program</p>
          </div>
          <div className="text-right">
            <div className="text-h2 text-black">{userProgress.overallProgress}%</div>
            <div className="text-body-small text-gray-600">Program Complete</div>
          </div>
        </div>

        {/* Progress Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div className="text-center">
                  <div className="text-h1 text-black mb-1">{userProgress.completedModules}/{userProgress.totalModules}</div>
                  <div className="text-body-small text-gray-600">Modules</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-black mb-1">{userProgress.currentStreak}</div>
                  <div className="text-body-small text-gray-600">Day Streak</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-black mb-1">{userProgress.totalPoints}</div>
                  <div className="text-body-small text-gray-600">Points</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-black mb-1">12</div>
                  <div className="text-body-small text-gray-600">Weeks Left</div>
                </div>
                <div className="text-center">
                  <div className="text-h1 text-black mb-1">A+</div>
                  <div className="text-body-small text-gray-600">Grade</div>
                </div>
              </div>
              <div className="mt-6">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-700">Overall Progress</span>
                  <span className="text-gray-700">{userProgress.overallProgress}%</span>
                </div>
                <Progress value={userProgress.overallProgress} className="h-3" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 rounded-lg p-1">
          {[
            { id: "dashboard", label: "Dashboard", icon: BarChart3 },
            { id: "milestones", label: "Milestones", icon: Target }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors flex-1 justify-center ${
                activeTab === tab.id
                  ? "bg-black text-white"
                  : "text-gray-600 hover:text-black hover:bg-gray-200"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === "dashboard" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Current Focus */}
            <div className="lg:col-span-2 space-y-6">


            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Upcoming Sessions */}
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-black text-lg">Upcoming Sessions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {upcomingSessions.map((session) => (
                      <div key={session.id} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <Video className="w-4 h-4 text-black" />
                          <span className="text-black text-sm font-medium">{session.title}</span>
                        </div>
                        <div className="text-gray-600 text-xs">{session.date} at {session.time}</div>
                        <div className="text-gray-600 text-xs">with {session.mentor}</div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4 bg-black hover:bg-gray-800 text-white" size="sm">
                    <Calendar className="w-4 h-4 mr-2" />
                    View All Sessions
                  </Button>
                </CardContent>
              </Card>

              {/* Next Milestone */}
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-black text-lg">Next Milestone</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Target className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-black font-semibold mb-2">{userProgress.nextMilestone}</h3>
                    <p className="text-gray-600 text-sm mb-4">Complete 2 more modules to unlock</p>
                    <Progress value={75} className="h-2 mb-3" />
                    <div className="text-gray-700 text-sm">75% Progress</div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="bg-white border-gray-200 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-black text-lg">This Week</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-black" />
                        <span className="text-gray-700 text-sm">Lessons Completed</span>
                      </div>
                      <span className="text-black font-semibold">12</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-black" />
                        <span className="text-gray-700 text-sm">Points Earned</span>
                      </div>
                      <span className="text-black font-semibold">450</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}



        {activeTab === "milestones" && (
          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-white border-gray-200 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-black font-semibold text-lg">{milestone.title}</h3>
                        <p className="text-gray-600">{milestone.description}</p>
                      </div>
                      <Badge className={getStatusColor(milestone.status)}>
                        {milestone.status === "in-progress" ? "In Progress" : milestone.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.current}</div>
                        <div className="text-gray-600 text-sm">Current</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.progress}%</div>
                        <div className="text-gray-600 text-sm">Progress</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.target}</div>
                        <div className="text-gray-600 text-sm">Target</div>
                      </div>
                    </div>
                    
                    <Progress value={milestone.progress} className="h-3 mb-4" />
                    
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <Award className="w-4 h-4 text-black" />
                        <span className="text-gray-700 text-sm font-medium">Reward:</span>
                        <span className="text-black text-sm">{milestone.reward}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}


      </div>
    </div>
  );
}
