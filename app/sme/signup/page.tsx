"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Logo from "@/components/ui/logo";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, Building, Calendar, DollarSign, Mail, MapPin, Phone, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function SMESignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phoneCountryCode: "+91",
    phone: "",

    // Company Info
    companyName: "",
    industry: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    country: "India",

    // Business Details
    yearEstablished: "",
    employeeCount: "",
    annualRevenue: "",
    businessType: "",

    // Goals
    fundingGoal: "",
    useOfFunds: "",
    timeline: ""
  });

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex justify-center mb-6">
            <Logo size="lg" />
          </div>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-black">SME Registration</h1>
            <span className="text-gray-600">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={progress} className="h-2 mb-2" />
          <p className="text-gray-600 text-sm">Complete your profile to get started with 10xCFO</p>
        </motion.div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="bg-white border-gray-200 shadow-lg">
            <CardHeader>
              <CardTitle className="text-black flex items-center">
                {currentStep === 1 && <><User className="w-5 h-5 mr-2 text-black" />Personal Information</>}
                {currentStep === 2 && <><Building className="w-5 h-5 mr-2 text-black" />Company Details</>}
                {currentStep === 3 && <><DollarSign className="w-5 h-5 mr-2 text-black" />Business Information</>}
                {currentStep === 4 && <><ArrowRight className="w-5 h-5 mr-2 text-black" />Funding Goals</>}
              </CardTitle>
              <CardDescription className="text-gray-600">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Provide your company information"}
                {currentStep === 3 && "Share your business details"}
                {currentStep === 4 && "Define your funding objectives"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-gray-700">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-gray-700">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="Doe"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-gray-700">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData("email", e.target.value)}
                        className="pl-10 bg-white border-gray-300 text-black"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-gray-700">Phone Number *</Label>
                    <div className="flex gap-2">
                      <select
                        value={formData.phoneCountryCode}
                        onChange={(e) => updateFormData("phoneCountryCode", e.target.value)}
                        className="w-24 bg-white border border-gray-300 text-black rounded-md px-2 py-2"
                      >
                        <option value="+91">+91</option>
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                        <option value="+61">+61</option>
                        <option value="+65">+65</option>
                        <option value="+971">+971</option>
                        <option value="+86">+86</option>
                        <option value="+81">+81</option>
                        <option value="+33">+33</option>
                        <option value="+49">+49</option>
                      </select>
                      <div className="relative flex-1">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => updateFormData("phone", e.target.value)}
                          className="pl-10 bg-white border-gray-300 text-black"
                          placeholder="98765 43210"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Company Details */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="companyName" className="text-gray-700">Company Name *</Label>
                    <div className="relative">
                      <Building className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="companyName"
                        value={formData.companyName}
                        onChange={(e) => updateFormData("companyName", e.target.value)}
                        className="pl-10 bg-white border-gray-300 text-black"
                        placeholder="Your Company Ltd."
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="industry" className="text-gray-700">Industry *</Label>
                    <select
                      id="industry"
                      value={formData.industry}
                      onChange={(e) => updateFormData("industry", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Industry</option>
                      <option value="technology">Technology</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="retail">Retail</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="finance">Finance</option>
                      <option value="education">Education</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="address" className="text-gray-700">Business Address *</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => updateFormData("address", e.target.value)}
                        className="pl-10 bg-white border-gray-300 text-black"
                        placeholder="Street Address"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="city" className="text-gray-700">City *</Label>
                      <select
                        id="city"
                        value={formData.city}
                        onChange={(e) => updateFormData("city", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="">Select City</option>
                        <option value="mumbai">Mumbai</option>
                        <option value="delhi">Delhi</option>
                        <option value="bangalore">Bangalore</option>
                        <option value="hyderabad">Hyderabad</option>
                        <option value="chennai">Chennai</option>
                        <option value="kolkata">Kolkata</option>
                        <option value="pune">Pune</option>
                        <option value="ahmedabad">Ahmedabad</option>
                        <option value="jaipur">Jaipur</option>
                        <option value="surat">Surat</option>
                        <option value="lucknow">Lucknow</option>
                        <option value="kanpur">Kanpur</option>
                        <option value="nagpur">Nagpur</option>
                        <option value="indore">Indore</option>
                        <option value="thane">Thane</option>
                        <option value="bhopal">Bhopal</option>
                        <option value="visakhapatnam">Visakhapatnam</option>
                        <option value="pimpri-chinchwad">Pimpri-Chinchwad</option>
                        <option value="patna">Patna</option>
                        <option value="vadodara">Vadodara</option>
                        <option value="ghaziabad">Ghaziabad</option>
                        <option value="ludhiana">Ludhiana</option>
                        <option value="agra">Agra</option>
                        <option value="nashik">Nashik</option>
                        <option value="faridabad">Faridabad</option>
                        <option value="meerut">Meerut</option>
                        <option value="rajkot">Rajkot</option>
                        <option value="kalyan-dombivali">Kalyan-Dombivali</option>
                        <option value="vasai-virar">Vasai-Virar</option>
                        <option value="varanasi">Varanasi</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="state" className="text-gray-700">State *</Label>
                      <select
                        id="state"
                        value={formData.state}
                        onChange={(e) => updateFormData("state", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="">Select State</option>
                        <option value="andhra-pradesh">Andhra Pradesh</option>
                        <option value="arunachal-pradesh">Arunachal Pradesh</option>
                        <option value="assam">Assam</option>
                        <option value="bihar">Bihar</option>
                        <option value="chhattisgarh">Chhattisgarh</option>
                        <option value="goa">Goa</option>
                        <option value="gujarat">Gujarat</option>
                        <option value="haryana">Haryana</option>
                        <option value="himachal-pradesh">Himachal Pradesh</option>
                        <option value="jharkhand">Jharkhand</option>
                        <option value="karnataka">Karnataka</option>
                        <option value="kerala">Kerala</option>
                        <option value="madhya-pradesh">Madhya Pradesh</option>
                        <option value="maharashtra">Maharashtra</option>
                        <option value="manipur">Manipur</option>
                        <option value="meghalaya">Meghalaya</option>
                        <option value="mizoram">Mizoram</option>
                        <option value="nagaland">Nagaland</option>
                        <option value="odisha">Odisha</option>
                        <option value="punjab">Punjab</option>
                        <option value="rajasthan">Rajasthan</option>
                        <option value="sikkim">Sikkim</option>
                        <option value="tamil-nadu">Tamil Nadu</option>
                        <option value="telangana">Telangana</option>
                        <option value="tripura">Tripura</option>
                        <option value="uttar-pradesh">Uttar Pradesh</option>
                        <option value="uttarakhand">Uttarakhand</option>
                        <option value="west-bengal">West Bengal</option>
                        <option value="delhi">Delhi</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="pincode" className="text-gray-700">Pincode *</Label>
                      <Input
                        id="pincode"
                        value={formData.pincode}
                        onChange={(e) => updateFormData("pincode", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="123456"
                      />
                    </div>
                    <div>
                      <Label htmlFor="country" className="text-gray-700">Country *</Label>
                      <select
                        id="country"
                        value={formData.country}
                        onChange={(e) => updateFormData("country", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="India">India</option>
                        <option value="United States">United States</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Canada">Canada</option>
                        <option value="Australia">Australia</option>
                        <option value="Singapore">Singapore</option>
                        <option value="UAE">UAE</option>
                        <option value="Germany">Germany</option>
                        <option value="France">France</option>
                        <option value="Japan">Japan</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Business Information */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="yearEstablished" className="text-gray-700">Year Established *</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                        <Input
                          id="yearEstablished"
                          value={formData.yearEstablished}
                          onChange={(e) => updateFormData("yearEstablished", e.target.value)}
                          className="pl-10 bg-white border-gray-300 text-black"
                          placeholder="2020"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="employeeCount" className="text-gray-700">Employee Count *</Label>
                      <select
                        id="employeeCount"
                        value={formData.employeeCount}
                        onChange={(e) => updateFormData("employeeCount", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="">Select Range</option>
                        <option value="1-10">1-10</option>
                        <option value="11-50">11-50</option>
                        <option value="51-200">51-200</option>
                        <option value="201-500">201-500</option>
                        <option value="500+">500+</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="annualRevenue" className="text-gray-700">Annual Revenue *</Label>
                    <select
                      id="annualRevenue"
                      value={formData.annualRevenue}
                      onChange={(e) => updateFormData("annualRevenue", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Range</option>
                      <option value="0-130K">$0 - $130K</option>
                      <option value="130K-650K">$130K - $650K</option>
                      <option value="650K-1.3M">$650K - $1.3M</option>
                      <option value="1.3M-6.5M">$1.3M - $6.5M</option>
                      <option value="6.5M-13M">$6.5M - $13M</option>
                      <option value="13M+">$13M+</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="businessType" className="text-gray-700">Business Type *</Label>
                    <select
                      id="businessType"
                      value={formData.businessType}
                      onChange={(e) => updateFormData("businessType", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Type</option>
                      <option value="product">Product-based</option>
                      <option value="service">Service-based</option>
                      <option value="hybrid">Hybrid (Product + Service)</option>
                      <option value="trading">Trading</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="others">Others</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Step 4: Funding Goals */}
              {currentStep === 4 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="fundingGoal" className="text-gray-700">Funding Goal *</Label>
                    <select
                      id="fundingGoal"
                      value={formData.fundingGoal}
                      onChange={(e) => updateFormData("fundingGoal", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Amount</option>
                      <option value="130K-650K">$130K - $650K</option>
                      <option value="650K-1.3M">$650K - $1.3M</option>
                      <option value="1.3M-6.5M">$1.3M - $6.5M</option>
                      <option value="6.5M-13M">$6.5M - $13M</option>
                      <option value="13M+">$13M+</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="useOfFunds" className="text-gray-700">Primary Use of Funds *</Label>
                    <select
                      id="useOfFunds"
                      value={formData.useOfFunds}
                      onChange={(e) => updateFormData("useOfFunds", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Purpose</option>
                      <option value="expansion">Business Expansion</option>
                      <option value="equipment">Equipment Purchase</option>
                      <option value="inventory">Inventory/Working Capital</option>
                      <option value="marketing">Marketing & Sales</option>
                      <option value="technology">Technology Upgrade</option>
                      <option value="hiring">Team Expansion</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="timeline" className="text-gray-700">Funding Timeline *</Label>
                    <select
                      id="timeline"
                      value={formData.timeline}
                      onChange={(e) => updateFormData("timeline", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Timeline</option>
                      <option value="immediate">Immediate (Within 1 month)</option>
                      <option value="3months">Within 3 months</option>
                      <option value="6months">Within 6 months</option>
                      <option value="1year">Within 1 year</option>
                      <option value="flexible">Flexible timeline</option>
                    </select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-between mt-8"
        >
          <div>
            {currentStep > 1 ? (
              <Button variant="outline" onClick={prevStep} className="border-gray-300 text-black hover:bg-gray-50">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            ) : (
              <Link href="/sme">
                <Button variant="outline" className="border-gray-300 text-black hover:bg-gray-50">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to SME Page
                </Button>
              </Link>
            )}
          </div>

          <div>
            {currentStep < totalSteps ? (
              <Button onClick={nextStep} className="bg-black hover:bg-gray-800 text-white">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Link href="/sme/dashboard">
                <Button className="bg-black hover:bg-gray-800 text-white">
                  Complete Registration
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
